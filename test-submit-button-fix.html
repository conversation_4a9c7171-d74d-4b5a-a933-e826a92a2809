<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Submit Button Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #1547bb;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        button:not(:disabled):hover {
            background-color: #121c41;
        }
        .role-suggestions {
            border: 1px solid #ccc;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            position: absolute;
            width: 100%;
            z-index: 1000;
        }
        .suggestion-tab {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        .suggestion-tab:hover {
            background-color: #f5f5f5;
        }
        .test-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Submit Button Fix Test</h1>
    <p>This test verifies that the submit button works correctly when selecting a role from the dropdown.</p>
    
    <form id="user-form">
        <div class="form-group">
            <label for="first-name">First Name</label>
            <input type="text" id="first-name" name="first-name" required>
        </div>
        
        <div class="form-group">
            <label for="last-name">Last Name</label>
            <input type="text" id="last-name" name="last-name" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group" style="position: relative;">
            <label for="role">Role</label>
            <input type="text" id="role" name="role" required placeholder="Type to search or select from dropdown">
        </div>
        
        <button type="submit" id="submit-form">Submit Assessment</button>
    </form>
    
    <div id="test-status" class="test-status" style="display: none;"></div>
    
    <script>
        // Mock common roles for testing
        const commonRoles = [
            "Administrative Assistant",
            "Marketing Executive", 
            "Sales Executive",
            "Project Manager",
            "Business Analyst",
            "Data Analyst",
            "Customer Service Advisor",
            "Operations Coordinator",
            "Finance Analyst",
            "HR Officer",
            "Product Manager",
            "Account Manager",
            "Executive Assistant",
            "IT Support Technician",
            "CEO"
        ];
        
        // Mock validation functions
        function validateRole(role) {
            console.log('validateRole called with:', role);
            return true;
        }
        
        // Copy the updateSubmitButton function from script2.js
        function updateSubmitButton() {
            const submitButton = document.querySelector('#user-form button[type="submit"]');
            if (submitButton) {
                const emailInput = document.getElementById('email');
                const roleInput = document.getElementById('role');
                const firstNameInput = document.getElementById('first-name');
                const lastNameInput = document.getElementById('last-name');

                const hasRequiredFields = emailInput?.value.trim() &&
                                         roleInput?.value.trim() &&
                                         firstNameInput?.value.trim() &&
                                         lastNameInput?.value.trim();

                submitButton.disabled = !hasRequiredFields;

                console.log('updateSubmitButton called:', {
                    hasRequiredFields,
                    emailValue: emailInput?.value.trim(),
                    roleValue: roleInput?.value.trim(),
                    firstNameValue: firstNameInput?.value.trim(),
                    lastNameValue: lastNameInput?.value.trim(),
                    buttonDisabled: submitButton.disabled
                });

                if (submitButton.disabled) {
                    submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                    submitButton.classList.remove('hover:bg-blue-600');
                } else {
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    submitButton.classList.add('hover:bg-blue-600');
                }
            }
        }
        
        // Create role suggestions functionality
        function createRoleSuggestions() {
            const roleInput = document.getElementById('role');
            if (!roleInput) return;

            const suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'role-suggestions';
            suggestionsContainer.style.display = 'none';

            roleInput.parentElement.appendChild(suggestionsContainer);

            function updateSuggestions(searchTerm) {
                const filteredRoles = commonRoles.filter(role =>
                    role.toLowerCase().includes(searchTerm.toLowerCase())
                );

                suggestionsContainer.innerHTML = '';

                if (filteredRoles.length === 0) {
                    suggestionsContainer.style.display = 'none';
                    return;
                }
                
                filteredRoles.forEach(role => {
                    const tab = document.createElement('div');
                    tab.textContent = role;
                    tab.className = 'suggestion-tab';

                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        e.stopPropagation();

                        // Update input value
                        roleInput.value = role;

                        // Hide suggestions
                        suggestionsContainer.style.display = 'none';

                        // Trigger validation
                        if (typeof validateRole === 'function') {
                            validateRole(role);
                        }

                        // Ensure submit button state is updated after role selection
                        setTimeout(() => {
                            updateSubmitButton();
                            showTestResult('Role selected: ' + role + '. Submit button should now be enabled if all fields are filled.', 'success');
                        }, 100);
                    });

                    suggestionsContainer.appendChild(tab);
                });

                suggestionsContainer.style.display = 'block';
            }

            // Input event for filtering
            roleInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.trim();
                updateSubmitButton(); // Update button on input

                if (searchTerm === '') {
                    suggestionsContainer.style.display = 'none';
                } else {
                    updateSuggestions(searchTerm);
                }
            });

            // Focus event to show suggestions
            roleInput.addEventListener('focus', () => {
                const searchTerm = roleInput.value.trim();
                if (searchTerm) {
                    updateSuggestions(searchTerm);
                } else {
                    updateSuggestions('');
                }
            });

            // Click outside to close suggestions
            document.addEventListener('click', (e) => {
                if (!roleInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                    suggestionsContainer.style.display = 'none';
                }
            });
        }
        
        function showTestResult(message, type) {
            const statusDiv = document.getElementById('test-status');
            statusDiv.textContent = message;
            statusDiv.className = 'test-status ' + type;
            statusDiv.style.display = 'block';
        }
        
        // Add event listeners for all form fields
        document.getElementById('first-name').addEventListener('input', updateSubmitButton);
        document.getElementById('last-name').addEventListener('input', updateSubmitButton);
        document.getElementById('email').addEventListener('input', updateSubmitButton);
        document.getElementById('role').addEventListener('input', updateSubmitButton);
        
        // Form submission handler
        document.getElementById('user-form').addEventListener('submit', (e) => {
            e.preventDefault();
            showTestResult('Form submitted successfully! The fix is working.', 'success');
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            createRoleSuggestions();
            updateSubmitButton();
            showTestResult('Test page loaded. Fill in all fields and try selecting a role from the dropdown.', 'success');
        });
    </script>
</body>
</html>
